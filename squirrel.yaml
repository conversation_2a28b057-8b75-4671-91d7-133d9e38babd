# Squirrel settings
# encoding: utf-8

# 要比共享目錄同名文件版本號更大方可生效
config_version: 'LTS'

# options: last | default | _custom_
# last: the last used latin keyboard layout
# default: US (ABC) keyboard layout
# _custom_: keyboard layout of your choice, e.g. 'com.apple.keylayout.USExtended' or simply 'USExtended'
keyboard_layout: default

# for veteran chord-typist
chord_duration: 0.1  # seconds

# options: always | never | appropriate
show_notifications_when: appropriate

style:
  inline_preedit: true    # 在文本框顯示輸入串
  inline_candidate: false # 在文本框顯示首選項
  translucency: true      # 透明度總開關

  # 淺色主题
  color_scheme: native # phub
  # 深色主题
  color_scheme_dark: native # phub

  # 默認界面設置 (會被具體主題覆寫)
  font_point: 14
  font_face: LXGW WenKai
  corner_radius: 5
  # hilited_corner_radius: 5
  line_spacing: 8
  hilite_spacing: 6
  spacing: 12
  text_orientation: horizontal  # horizontal | vertical

preset_color_schemes:
  native:
    name: 系統配色

  aqua:
    name: 碧水／Aqua
    author: 佛振 <<EMAIL>>
    text_color: 0x606060
    back_color: 0xeeeceeee
    candidate_text_color: 0x000000
    hilited_text_color: 0x000000
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0xeefa3a0a
    comment_text_color: 0x5a5a5a
    hilited_comment_text_color: 0xfcac9d

  azure:
    name: 青天／Azure
    author: 佛振 <<EMAIL>>
    text_color: 0xcfa677
    candidate_text_color: 0xffeacc
    back_color: 0xee8b4e01
    hilited_text_color: 0xffeacc
    hilited_candidate_text_color: 0x7ffeff
    hilited_candidate_back_color: 0x00000000
    comment_text_color: 0xc69664

  luna:
    name: 明月／Luna
    author: 佛振 <<EMAIL>>
    text_color: 0xa5a5a5
    back_color: 0xdd000000
    candidate_text_color: 0xeceeee
    hilited_text_color: 0x7fffff
    hilited_candidate_text_color: 0x7fffff
    hilited_candidate_back_color: 0x40000000
    comment_text_color: 0xa5a5a5
    hilited_comment_text_color: 0x449c9d

  lost_temple:
    name: 孤寺／Lost Temple
    author: 佛振 <<EMAIL>>, based on ir_black
    text_color: 0xe8f3f6
    back_color: 0xee303030
    hilited_text_color: 0x82e6ca
    hilited_candidate_text_color: 0x000000
    hilited_candidate_back_color: 0x82e6ca
    comment_text_color: 0xbb82e6ca
    hilited_comment_text_color: 0xbb203d34

  dark_temple:
    name: 暗堂／Dark Temple
    author: 佛振 <<EMAIL>>, based on ir_black
    text_color: 0x92f6da
    back_color: 0x222222
    candidate_text_color: 0xd8e3e6
    hilited_text_color: 0xffcf9a
    hilited_back_color: 0x222222
    hilited_candidate_text_color: 0x92f6da
    hilited_candidate_back_color: 0x10000000  # 0x333333
    comment_text_color: 0x606cff

  purity_of_form:
    name: 純粹的形式／Purity of Form
    author: 雨過之後、佛振
    text_color: 0xc2c2c2
    back_color: 0x444444
    candidate_text_color: 0xeeeeee
    hilited_text_color: 0xeeeeee
    hilited_back_color: 0x444444
    hilited_candidate_text_color: 0x000000
    hilited_candidate_back_color: 0xfafafa
    comment_text_color: 0x808080

  purity_of_essence:
    name: 純粹的本質／Purity of Essence
    author: 佛振
    text_color: 0x2c2ccc
    back_color: 0xfafafa
    candidate_text_color: 0x000000
    hilited_text_color: 0x000000
    hilited_back_color: 0xfafafa
    hilited_candidate_text_color: 0xeeeeee
    hilited_candidate_back_color: 0x444444
    comment_text_color: 0x808080

  solarized_rock:
    name: 曬經石／Solarized Rock
    author: "Aben <<EMAIL>>, based on Ethan Schoonover's Solarized color scheme"
    back_color: 0x362b00
    border_color: 0x362b00
    text_color: 0x8236d3
    hilited_text_color: 0x98a12a
    candidate_text_color: 0x969483
    comment_text_color: 0xc098a12a
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0x8236d3
    hilited_comment_text_color: 0x362b00

  clean_white:
    name: 简约白／Clean White
    author: Chongyu Zhu <<EMAIL>>, based on 搜狗「简约白」
    candidate_list_layout: linear
    candidate_format: '%c %@'
    corner_radius: 6
    border_height: 6
    border_width: 6
    font_point: 16
    label_font_point: 12
    label_color: 0x888888
    text_color: 0x808080
    hilited_text_color: 0x000000
    candidate_text_color: 0x000000
    comment_text_color: 0x808080
    back_color: 0xeeeeee
    hilited_candidate_label_color: 0xa0c98915
    hilited_candidate_text_color: 0xc98915
    hilited_candidate_back_color: 0xeeeeee

  apathy:
    name: 冷漠／Apathy
    author: LIANG Hai
    candidate_list_layout: linear  # 水平排列
    inline_preedit: true #单行显示，false双行显示
    candidate_format: "%c\u2005%@\u2005"  # 编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5  #候选条圆角
    border_height: 0
    border_width: 0
    back_color: 0xFFFFFF  #候选条背景色
    font_face: "PingFangSC-Regular,HanaMinB"  #候选词字体
    font_point: 16  #候选字词大小
    text_color: 0x424242  #高亮选中词颜色
    label_font_face: "STHeitiSC-Light"   #候选词编号字体
    label_font_point: 12   #候选编号大小
    hilited_candidate_text_color: 0xEE6E00  #候选文字颜色
    hilited_candidate_back_color: 0xFFF0E4  #候选文字背景色
    comment_text_color: 0x999999  #拼音等提示文字颜色

  dust:
    name: 浮尘／Dust
    author: Superoutman <<EMAIL>>
    candidate_list_layout: linear  # 水平排列
    inline_preedit: true #单行显示，false双行显示
    candidate_format: "%c\u2005%@\u2005"  # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间。
    corner_radius: 2  #候选条圆角
    border_height: 3                                   # 窗口边界高度，大于圆角半径才生效
    border_width: 8                                    # 窗口边界宽度，大于圆角半径才生效
    back_color: 0xeeffffff  #候选条背景色
    border_color: 0xE0B693                             # 边框色
    font_face: "HYQiHei-55S Book,HanaMinA Regular"     #候选词字体
    font_point: 14  #候选字词大小
    label_font_face: "SimHei"   #候选词编号字体
    label_font_point: 10   #候选编号大小
    label_color: 0xcbcbcb                              # 预选栏编号颜色
    candidate_text_color: 0x555555                     # 预选项文字颜色
    text_color: 0x424242                               # 拼音行文字颜色，24位色值，16进制，BGR顺序
    comment_text_color: 0x999999                       # 拼音等提示文字颜色
    hilited_text_color: 0x9e9e9e                       # 高亮拼音 (需要开启内嵌编码)
    hilited_candidate_text_color: 0x000000             # 第一候选项文字颜色
    hilited_candidate_back_color: 0xfff0e4             # 第一候选项背景背景色
    hilited_candidate_label_color: 0x555555            # 第一候选项编号颜色
    hilited_comment_text_color: 0x9e9e9e               # 注解文字高亮

  mojave_dark:
    name: 沙漠夜／Mojave Dark
    author: xiehuc <<EMAIL>>
    candidate_list_layout: linear                        # 水平排列
    inline_preedit: true                    # 单行显示，false双行显示
    candidate_format: "%c\u2005%@"    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间。
    corner_radius: 5                        # 候选条圆角
    hilited_corner_radius: 3                # 高亮圆角
    border_height: 6                        # 窗口边界高度，大于圆角半径才生效
    border_width: 6                         # 窗口边界宽度，大于圆角半径才生效
    font_face: "PingFangSC"                 # 候选词字体
    font_point: 16                          # 候选字词大小
    label_font_point: 14                    # 候选编号大小

    text_color: 0xdedddd                    # 拼音行文字颜色，24位色值，16进制，BGR顺序
    back_color: 0x252320                    # 候选条背景色
    label_color: 0x888785                   # 预选栏编号颜色
    border_color: 0x020202                  # 边框色
    candidate_text_color: 0xdedddd          # 预选项文字颜色
    hilited_text_color: 0xdedddd            # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0x252320            # 高亮拼音 (需要开启内嵌编码)
    hilited_candidate_text_color: 0xffffff  # 第一候选项文字颜色
    hilited_candidate_back_color: 0xcb5d00  # 第一候选项背景背景色
    hilited_candidate_label_color: 0xffffff # 第一候选项编号颜色
    comment_text_color: 0xdedddd            # 拼音等提示文字颜色
    #hilited_comment_text_color: 0xdedddd    # 注解文字高亮

  solarized_light:
    name: 曬經・日／Solarized Light
    author: 雪齋 <<EMAIL>>
    color_space: display_p3 # Only available on macOS 10.12+
    back_color: 0xF0E5F6FB                  #Lab 97 , 0 , 10
    border_color: 0xEDFFFF                  #Lab 100, 0 , 10
    preedit_back_color: 0x403516            #Lab 20 ,-12,-12
    candidate_text_color: 0x595E00          #Lab 35 ,-35,-5
    label_color: 0xA36407                   #Lab 40 ,-10,-45
    comment_text_color: 0x005947            #Lab 35 ,-20, 65
    text_color: 0xA1A095                    #Lab 65 ,-5 ,-2
    hilited_back_color: 0x4C4022            #Lab 25 ,-12,-12
    hilited_candidate_back_color: 0xD7E8ED  #Lab 92 , 0 , 10
    hilited_candidate_text_color: 0x3942CB  #Lab 50 , 65, 45
    hilited_candidate_label_color: 0x2566C6 #Lab 55 , 45, 65
    hilited_comment_text_color: 0x8144C2    #Lab 50 , 65,-5
    hilited_text_color: 0x2C8BAE            #Lab 60 , 10, 65

  solarized_dark:
    name: 曬經・月／Solarized Dark
    author: 雪齋 <<EMAIL>>
    back_color: 0xF0352A0A                  #Lab 15 ,-12,-12
    border_color: 0x2A1F00                  #Lab 10 ,-12,-12
    preedit_back_color: 0xD7E8ED            #Lab 92 , 0 , 10
    candidate_text_color: 0x7389FF          #Lab 75 , 65, 45
    label_color: 0x478DF4                   #Lab 70 , 45, 65
    comment_text_color: 0xC38AFF            #Lab 75 , 65,-5
    text_color: 0x756E5D                    #Lab 45 ,-7 ,-7
    hilited_back_color: 0xC9DADF            #Lab 87 , 0 , 10
    hilited_candidate_back_color: 0x403516  #Lab 20 ,-12,-12
    hilited_candidate_text_color: 0x989F52  #Lab 60 ,-35,-5
    hilited_candidate_label_color: 0xCC8947 #Lab 55 ,-10,-45
    hilited_comment_text_color: 0x289989    #Lab 60 ,-20, 65
    hilited_text_color: 0xBE706D            #Lab 50 , 15,-45

  mac_light:
    name: Mac浅色
    candidate_format: "%c %@ "   # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5                             # 窗口圆角
    hilited_corner_radius: 5                     # 高亮圆角
    line_spacing: 10                             # 行间距(适用于竖排)
    border_height: 4                             # 窗口上下高度，大于圆角半径才生效
    border_width: 4                              # 窗口左右宽度，大于圆角半径才生效
    font_face: "PingFangSC"                      # 候选词字体
    font_point: 16                               # 候选字大小
    label_font_point: 13                         # 候选编号大小
    text_color: 0x424242                    # 拼音行文字颜色
    back_color: 0xFFFFFF                    # 候选条背景色
    border_color: 0xFFFFFF                  # 边框色
    label_color: 0x999999                   # 预选栏编号颜色
    candidate_text_color: 0x3c3c3c          # 预选项文字颜色
    comment_text_color: 0x999999            # 拼音等提示文字颜色
    hilited_text_color: 0x999999            # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0xD75A00            # 第一候选项背景背景色
    hilited_candidate_text_color: 0xFFFFFF  # 第一候选项文字颜色
    hilited_candidate_label_color: 0xFFFFFF # 第一候选项编号颜色
    hilited_comment_text_color: 0x999999    # 注解文字高亮

  mac_dark:
    name: Mac深色
    candidate_format: "%c %@ "   # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5                             # 窗口圆角
    hilited_corner_radius: 5                     # 高亮圆角
    line_spacing: 10                              # 行间距(适用于竖排)
    border_height: 4                             # 窗口上下高度，大于圆角半径才生效
    border_width: 4                              # 窗口左右宽度，大于圆角半径才生效
    font_face: "PingFangSC"                      # 候选词字体
    font_point: 16                               # 候选字大小
    label_font_point: 13                         # 候选编号大小
    text_color: 0x424242                    # 拼音行文字颜色
    back_color: 0x252a2e                    # 候选条背景色
    border_color: 0x050505                  # 边框色
    label_color: 0x999999                   # 预选栏编号颜色
    candidate_text_color: 0xe9e9ea          # 预选项文字颜色
    comment_text_color: 0x999999            # 拼音等提示文字颜色
    hilited_text_color: 0x999999            # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0xD75A00            # 第一候选项背景背景色
    hilited_candidate_text_color: 0xFFFFFF  # 第一候选项文字颜色
    hilited_candidate_label_color: 0xFFFFFF # 第一候选项编号颜色
    hilited_comment_text_color: 0x999999    # 注解文字高亮


  mac_green:
    name: Mac绿
    candidate_format: "%c %@ "   # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5                             # 窗口圆角
    hilited_corner_radius: 5                     # 高亮圆角
    line_spacing: 10                              # 行间距(适用于竖排)
    border_height: 4                             # 窗口上下高度，大于圆角半径才生效
    border_width: 4                              # 窗口左右宽度，大于圆角半径才生效
    font_face: "PingFangSC"                      # 候选词字体
    font_point: 16                               # 候选字大小
    label_font_point: 13                         # 候选编号大小
    text_color: 0x424242                    # 拼音行文字颜色
    back_color: 0xFFFFFF                    # 候选条背景色
    border_color: 0xFFFFFF                  # 边框色
    label_color: 0x999999                   # 预选栏编号颜色
    candidate_text_color: 0x3c3c3c          # 预选项文字颜色
    comment_text_color: 0x999999            # 拼音等提示文字颜色
    hilited_text_color: 0x999999            # 高亮拼音 (需要开启内嵌编码)
    hilited_candidate_back_color: 0x32A14C  # 第一候选项背景色
    hilited_candidate_text_color: 0xFFFFFF  # 第一候选项文字颜色
    hilited_candidate_label_color: 0xFFFFFF # 第一候选项编号颜色
    hilited_comment_text_color: 0x999999    # 注解文字高亮

  mac_orange:
    name: Mac橙
    candidate_format: "%c %@ "   # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5                             # 窗口圆角
    hilited_corner_radius: 5                     # 高亮圆角
    line_spacing: 10                              # 行间距(适用于竖排)
    border_height: 4                             # 窗口上下高度，大于圆角半径才生效
    border_width: 4                              # 窗口左右宽度，大于圆角半径才生效
    font_face: "PingFangSC"                      # 候选词字体
    font_point: 16                               # 候选字大小
    label_font_point: 13                         # 候选编号大小
    text_color: 0x424242                    # 拼音行文字颜色
    back_color: 0xFFFFFF                    # 候选条背景色
    border_color: 0xFFFFFF                  # 边框色
    label_color: 0x999999                   # 预选栏编号颜色
    candidate_text_color: 0x3c3c3c          # 预选项文字颜色
    comment_text_color: 0x999999            # 拼音等提示文字颜色
    hilited_text_color: 0x999999            # 高亮拼音 (需要开启内嵌编码)
    hilited_candidate_back_color: 0x0E6BD8  # 第一候选项背景色
    hilited_candidate_text_color: 0xFFFFFF  # 第一候选项文字颜色
    hilited_candidate_label_color: 0xFFFFFF # 第一候选项编号颜色
    hilited_comment_text_color: 0x999999    # 注解文字高亮


  mac_blue:
    name: Mac浅蓝
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5                             # 窗口圆角
    font_face: "PingFangSC"                      # 候选词字体
    font_point: 16                               # 候选字大小
    label_font_point: 13                         # 候选编号大小
    line_spacing: 10                              # 行间距(适用于竖排)
    text_color: 0x424242                    # 拼音行文字颜色
    back_color: 0xFFFFFF                    # 候选条背景色
    border_color: 0xFFFFFF                  # 边框色
    label_color: 0x999999                   # 预选栏编号颜色
    candidate_text_color: 0x3c3c3c          # 预选项文字颜色
    comment_text_color: 0x999999            # 拼音等提示文字颜色
    hilited_text_color: 0x999999            # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0xF8AA4D            # 第一候选项背景背景色
    hilited_candidate_text_color: 0xFFFFFF  # 第一候选项文字颜色
    hilited_candidate_label_color: 0xFFFFFF # 第一候选项编号颜色
    hilited_comment_text_color: 0x999999    # 注解文字高亮

  psionics:
    name: 幽能
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5                             # 窗口圆角
    font_point: 16                               # 候选文字大小
    label_font_point: 14                         # 候选编号大小
    line_spacing: 10                              # 行间距(适用于竖排)
    text_color: 0xc2c2c2                     # 拼音行文字颜色
    back_color: 0x444444                     # 候选条背景色，24位色值，16进制，BGR顺序
    candidate_text_color: 0xeeeeee           # 预选项文字颜色
    hilited_candidate_back_color: 0xd4bc00   # 候选文字背景色
    comment_text_color: 0x808080             # 拼音等提示文字颜色
    hilited_text_color: 0xeeeeee             # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0x444444             # 第一候选项背景背景色
    hilited_candidate_label_color: 0xfafafa  # 第一候选项编号颜色
    hilited_candidate_text_color: 0xfafafa   # 第一候选项文字颜色
    hilited_comment_text_color: 0x444444     # 注解文字高亮

  win10:
    candidate_format: "%c %@  "       # 如果是竖排建议改为 "%c\u2005%@"
    font_point: 17              # 候选文字大小
    label_font_point: 16        # 候选编号大小
    corner_radius: 0            # 候选条圆角
    border_width: 6             # 窗口左右宽度
    border_height: 6            # 窗口左右高度
    margin_x: 12                # 字起始距左边距离
    margin_y: 12                # 字上下边距
    spacing: 10                 # 间距
    candidate_spacing: 24       # 候选字间隔
    hilite_spacing: 8           # 序号和候选字之间的间隔
    hilite_padding: 12          # 候选字背景色色块高度 若想候选字背景色块无边界填充候选框，仅需其高度和候选字上下边距一致即可
    round_corner: 0             # 候选字背景色块圆角幅度
    line_spacing: 10            # 行间距(适用于竖排)
    candidate_text_color: 0x000000          # 预选项文字颜色
    comment_text_color: 0x888888            # 拼音等提示文字颜色
    text_color: 0x000000                    # 拼音行文字颜色
    back_color: 0xffffff                    # 候选条背景色，24位色值，16进制，BGR顺序
    hilited_text_color: 0x000000            # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0xffffff            # 第一候选项背景背景色
    hilited_candidate_text_color: 0xffffff  # 第一候选项文字颜色
    hilited_candidate_back_color: 0xcc8f29  # 候选文字背景色
    hilited_comment_text_color: 0xffffff    # 注解文字高亮
    hilited_label_color: 0xffffff           # 已选字的数字的颜色
    label_color: 0x888888                   # 预选栏编号颜色

  solarized_rock:
    name: 日光石
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 6                             # 窗口圆角
    font_point: 16                               # 候选文字大小
    label_font_point: 15                         # 候选编号大小
    line_spacing: 10                             # 行间距(适用于竖排)
    text_color: 0x8236d3                     # 拼音行文字颜色
    back_color: 0x362b00                     # 候选条背景色，24位色值，16进制，BGR顺序
    candidate_text_color: 0x969483           # 预选项文字颜色
    comment_text_color: 0xc098a12a           # 拼音等提示文字颜色
    hilited_text_color: 0x98a12a             # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0x444444             # 第一候选项背景背景色
    hilited_candidate_label_color: 0xfafafa  # 第一候选项编号颜色
    hilited_candidate_back_color: 0x8236d3   # 候选文字背景色
    hilited_candidate_text_color: 0xffffff   # 第一候选项文字颜色
    hilited_comment_text_color: 0x362b00     # 注解文字高亮

  solarized_dark:
    name: 夜光石
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    corner_radius: 6                             # 窗口圆角
    font_point: 16                               # 候选文字大小
    label_font_point: 15                         # 候选编号大小
    line_spacing: 10                             # 行间距(适用于竖排)
    text_color: 0xA1A095                     # 拼音行文字颜色
    border_color: 0xf02A1F00                 # 边框颜色
    label_color: 0xCC8947                    # 预选栏编号颜色
    back_color: 0xf0352A0A                   # 候选条背景色，24位色值，16进制，BGR顺序
    candidate_text_color: 0x989F52           # 预选项文字颜色
    comment_text_color: 0x289989             # 拼音等提示文字颜色
    hilited_text_color: 0x2C8BAE             # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0x444444             # 第一候选项背景背景色
    hilited_candidate_label_color: 0x2566C6  # 第一候选项编号颜色
    hilited_candidate_back_color: 0xD7E8ED   # 候选文字背景色
    hilited_candidate_text_color: 0x3942CB   # 第一候选项文字颜色
    hilited_comment_text_color: 0x8144C2     # 注解文字高亮

  google:
    name: 谷歌
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    font_point: 16                           # 候选文字大小
    label_font_point: 15                     # 候选编号大小
    corner_radius: 6                         # 窗口圆角
    line_spacing: 10                         # 行间距(适用于竖排)
    text_color: 0x666666                     # 拼音行文字颜色
    candidate_text_color: 0x000000           # 预选项文字颜色
    back_color: 0xFFFFFF                     # 候选条背景色
    border_color: 0xE2E2E2                   # 边框
    hilited_text_color: 0x000000             # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0xFFFFFF             # 第一候选项背景背景色
    hilited_candidate_text_color: 0xffffff   # 第一候选项文字颜色
    hilited_candidate_back_color: 0xCE7539   # 候选文字背景色
    comment_text_color: 0x6D6D6D             # 拼音等提示文字颜色
    hilited_comment_text_color: 0xEBC6B0     # 注解文字高亮
    hilited_candidate_label_color: 0xfafafa  # 第一候选项编号颜色

  milan:
    name: 米兰
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    font_point: 16                           # 候选文字大小
    label_font_point: 14                     # 候选编号大小
    line_spacing: 8                          # 行间距
    line_spacing: 10                         # 行间距(适用于竖排)
    border_height: 4                             # 窗口上下高度，大于圆角半径才生效
    border_width: 4                              # 窗口左右宽度，大于圆角半径才生效
    corner_radius: 5                         # 窗口圆角
    back_color: 0xFFFFFF                     # 候选条背景色
    comment_text_color: 0x999999             # 拼音等提示文字颜色
    hilited_candidate_back_color: 0xF5803B   # 候选文字背景色
    hilited_candidate_text_color: 0xFFFFFF   # 第一候选项文字颜色
    text_color: 0x424242                     # 拼音行文字颜色

  ink:
    name: 墨池
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    font_point: 16                           # 候选文字大小
    label_font_point: 14                     # 候选编号大小
    corner_radius: 5                         # 窗口圆角
    line_spacing: 10                         # 行间距(适用于竖排)
    text_color: 0x5a5a5a                     # 拼音等提示文字颜色
    back_color: 0xeeffffff                   # 候选条背景色
    candidate_text_color: 0x000000           # 第一候选项文字颜色
    hilited_text_color: 0x000000             # 高亮拼音 (需要开启内嵌编码)
    hilited_candidate_text_color: 0xffffff   # 第一候选项文字颜色
    hilited_candidate_back_color: 0xcc000000 # 候选文字背景色
    comment_text_color: 0x5a5a5a             # 拼音等提示文字颜色
    hilited_comment_text_color: 0x808080     # 注解文字高亮

  purity:
    name: 纯洁
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    font_point: 16                           # 候选文字大小
    label_font_point: 14                     # 候选编号大小
    corner_radius: 5                         # 窗口圆角
    line_spacing: 10                         # 行间距(适用于竖排)
    text_color: 0xc2c2c2                     # 拼音等提示文字颜色
    back_color: 0x444444                     # 候选条背景色
    candidate_text_color: 0xeeeeee           # 第一候选项文字颜色
    hilited_text_color: 0xeeeeee             # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0x444444             # 第一候选项背景背景色
    hilited_candidate_text_color: 0x000000   # 第一候选项文字颜色
    hilited_candidate_back_color: 0xfafafa   # 候选文字背景色
    comment_text_color: 0x808080             # 拼音等提示文字颜色

  starcraft:
    name: 星际争霸
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    font_point: 16                           # 候选文字大小
    label_font_point: 14                     # 候选编号大小
    corner_radius: 5                         # 窗口圆角
    line_spacing: 10                         # 行间距(适用于竖排)
    text_color: 0xccaa88                     # 拼音等提示文字颜色
    candidate_text_color: 0x30bb55           # 第一候选项文字颜色
    back_color: 0xee000000                   # 候选条背景色
    border_color: 0x1010a0                   # 边框色
    hilited_text_color: 0xfecb96             # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0x000000             # 第一候选项背景背景色
    hilited_candidate_text_color: 0x70ffaf   # 第一候选项文字颜色
    hilited_candidate_back_color: 0x000000   # 候选文字背景色
    comment_text_color: 0x1010d0             # 拼音等提示文字颜色
    hilited_comment_text_color: 0x1010f0     # 注解文字高亮

  nord_light:
    name: 北方浅色
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    font_point: 16                           # 候选文字大小
    label_font_point: 14                     # 候选编号大小
    corner_radius: 5                         # 窗口圆角
    line_spacing: 10                         # 行间距(适用于竖排)
    back_color: 0xF4EFEC                     # 候选条背景色
    border_color: 0xF4EFEC                   # 边框色
    candidate_text_color: 0xC1A181           # 第一候选项文字颜色
    comment_text_color: 0xD0C088             # 拼音等提示文字颜色
    hilited_back_color: 0xF0E9E5             # 第一候选项背景背景色
    hilited_candidate_back_color: 0xE9DED8   # 候选文字背景色
    hilited_candidate_text_color: 0xAC815E   # 第一候选项文字颜色
    hilited_text_color: 0xAD8EB4             # 高亮拼音 (需要开启内嵌编码)
    text_color: 0x7087D0                     # 拼音等提示文字颜色

  nord_dark:
    name: 北方深色
    candidate_format: "%c %@ "    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间
    font_point: 16                           # 候选文字大小
    label_font_point: 14                     # 候选编号大小
    corner_radius: 5                         # 窗口圆角
    line_spacing: 10                         # 行间距(适用于竖排)
    back_color: 0x473A33                     # 候选条背景色
    border_color: 0x473A33                   # 边框色
    candidate_text_color: 0xF5D887           # 第一候选项文字颜色
    comment_text_color: 0xE6B687             # 拼音等提示文字颜色
    hilited_back_color: 0x473A33             # 第一候选项背景背景色
    hilited_candidate_back_color: 0x5D4C43   # 候选文字背景色
    hilited_candidate_text_color: 0xC0E077   # 第一候选项文字颜色
    hilited_text_color: 0x6EC8F5             # 高亮拼音 (需要开启内嵌编码)
    text_color: 0x78E8F0                     # 拼音等提示文字颜色

  roseo_maple:
    name: 玫枫 / Roseo Maple
    author: "KyleBing <http://kylebing.cn>"
    alpha: 1.0                                # 候选窗口透明度：符点型数据，小数点形式，不然出错无法正常显示皮肤
    border_height: 5                          # 边距 - 面板上下
    border_width: 0                           # 宽度 - 边框
    border_color: 0xffffff                    # 颜色 - 边框
    back_color: 0xF4F4F6                      # 颜色 - 面板背景
    label_color: 0xaaaaaa                     # 颜色 - 候选序号
    # font_face: PingFang SC Semibold                    # 字体 - 候选文字
    font_point: 18                            # 字体大小 - 候选
    # label_font_face: Skia                     # 字体 - 候选数字
    # label_font_point: 18                      # 字体大小 - 候选序号
    corner_radius: 5                          # 候选框圆角大小


    # %c 是候选序号 %@ 是候选词
    candidate_format: "%c %@ "               # 候选词格式，后面多个空格是为了增加候选词之间的距离

    line_spacing: 5                          # horizontal 为 false 时，上下候选的间隔大小
    base_offset: 0                           # 文字基线调整

    preedit_back_color: 0x364572
    hilited_corner_radius: 5                  # 圆角 - 首选候选词
    hilited_candidate_text_color: 0x4F11FA    # 颜色 - 首选候选词 - 文字
    hilited_candidate_back_color: 0xEDEDF9    # 颜色 - 首选候选词 - 背景  8位的前两位是颜色透明度，00就是透明，FF就是不透明
    hilited_candidate_label_color: 0x884F11FA # 颜色 - 首选候选词 - 序号
    hilited_comment_text_color: 0xF19C38      # 颜色 - 首选候选词 - 提示编码
    candidate_text_color: 0x222222            # 颜色 - 其它候选词 - 文字
    comment_text_color: 0x5AC461              # 颜色 - 其它候选词 - 提示编码
    comment_font_face: PingFang SC            # 字体 - 候选词编码的提示
    comment_font_point: 16                    # 字体大小 - 候选词编码的提示

    inline_preedit: true                      # 输入的编码是否直接写入到输入框中，如果为 false，未上屏的编码就会写在候选框中
    spacing: 5                                # 当 inline_preedit 为 false 时，编码会出现在候选面板中，该值控制编码与候选词之间的距离
    # inline_preedit 为 false 时
    hilited_text_color: 0x8E8E93              # 颜色 - 输入中 - 编码
    hilited_back_color: 0xEFEFF4              # 颜色 - 输入中 - 背景

  calm:
    name: "安静"
    border_height: 5
    border_width: 10
    border_color: 0x8E8E93
    back_color: 0xEFEFF4
    label_color: 0x8E8E93
    font_point: 20
    label_font_point: 18
    corner_radius: 5
    candidate_format: "%c %@ "
    line_spacing: 5
    hilited_candidate_text_color: 0x000000
    hilited_candidate_back_color: 0x00ffffff
    hilited_candidate_label_color: 0x888888
    hilited_comment_text_color: 0xF19C38
    candidate_text_color: 0x888888
    comment_text_color: 0x5AC461
    inline_preedit: true
    spacing: 5
    hilited_text_color: 0x8E8E93
    hilited_back_color: 0xEFEFF4

  eucalyptus:
    author: "ID;Peace B"
    corner_radius: 5
    back_color: 0xFFFFFF
    border_color: 0xC9B881
    candidate_text_color: 0x8C7662
    comment_text_color: 0x8C7662
    hilited_back_color: 0xD5E2F2
    hilited_candidate_back_color: 0xD1A78E
    hilited_candidate_text_color: 0xFFFFFF
    hilited_comment_text_color: 0xFFFFFF
    name: "尤加利"
    text_color: 0x2B1007

  blackpink:
    author: swwrww
    corner_radius: 5
    back_color: 0x1e1e1e
    border_color: 0xff303030
    candidate_text_color: 0xaaa6f0
    comment_text_color: 0xaaa6f0
    hilited_back_color: 0xaaa6f0
    hilited_candidate_back_color: 0xA996F1
    hilited_candidate_text_color: 0x1e1e1e
    hilited_comment_text_color: 0x1e1e1e
    hilited_label_color: 0x1e1e1e
    hilited_mark_color: 0xf9f9f9
    hilited_text_color: 0xFFFFFF
    label_color: 0xaaa6f0
    name: "脏粉"
    text_color: 0xFFFFFF

  easy_dark:
    author: "木易"
    corner_radius: 5
    back_color: 0x36261F
    candidate_text_color: 0xDBDBDB
    comment_text_color: 0xA8A8A8
    hilited_back_color: 0x471885
    hilited_candidate_back_color: 0x63453A
    hilited_candidate_label_color: 0xFFFFFF
    hilited_candidate_text_color: 0xDBDBDB
    hilited_comment_text_color: 0xA8A8A8
    hilited_text_color: 0xD6D6D6
    label_color: 0xDBDBDB
    name: "蓝黑"
    text_color: 0xD6D6D6

  gruvbox_light:
    author: Q
    corner_radius: 5
    back_color: 0xc7f1fb
    border_color: 0xb2dbeb
    candidate_text_color: 0x888545
    comment_text_color: 0xa2577f
    hilited_back_color: 0xb2dbeb
    hilited_candidate_text_color: 0x000000
    hilited_label_color: 0x1a9798
    hilited_text_color: 0x1d24cc
    label_color: 0x0e5dd6
    name: "Gruvbox Light"
    text_color: 0x6a9d68

  pink:
    author: swwrww
    corner_radius: 5
    back_color: 0x877A73
    border_color: 0x877A73
    candidate_text_color: 0xBABABA
    comment_text_color: 0xFFFFFF
    hilited_back_color: 0xA996F1
    hilited_candidate_back_color: 0xA996F1
    hilited_candidate_text_color: 0xffffff
    hilited_mark_color: 0xf9f9f9
    hilited_text_color: 0xFFFFFF
    label_color: 0xFFFFFF
    name: "萌猫粉"
    text_color: 0xFFFFFF

  rose_red:
    author: "木易"
    corner_radius: 5
    back_color: 0xF4F4F6
    border_color: 0xffffff
    candidate_text_color: 0x383838
    comment_text_color: 0x87807B
    hilited_back_color: 0xEFEFF4
    hilited_candidate_back_color: 0xEDEDF9
    hilited_candidate_label_color: 0x884F11FA
    hilited_candidate_text_color: 0x4F11FA
    hilited_comment_text_color: 0xF19C38
    hilited_corner_radius: 5
    hilited_text_color: 0x8E8E93
    label_color: 0xaaaaaa
    name: "玫红"
    preedit_back_color: 0x364572

  so_young_qmod_01:
    author: Q
    corner_radius: 5
    back_color: 0xe3f6fd
    border_color: 0xc5c8cc
    candidate_text_color: 0x736b55
    comment_text_color: 0x9377b9
    hilited_candidate_back_color: 0x736b55
    hilited_candidate_text_color: 0xd5e8ee
    hilited_comment_text_color: 0xd5e8ee
    hilited_mark_color: 0xffffff
    hilited_text_color: 0x808070
    key_symbol_color: 0x339977
    label_color: 0x919183
    name: "致青春·Q改"
    preview_back_color: 0x6b55d073
    preview_text_color: 0xd5e8ee
    text_color: 0xbd8c32

  win10black:
    back_color: 0x000000
    border_color: 0x347440
    candidate_text_color: 0xffffff
    comment_text_color: 0x666666
    hilited_back_color: 0x347440
    hilited_candidate_back_color: 0x347440
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xa9a9a9
    hilited_label_color: 0xa9a9a9
    hilited_mark_color: 0xa9a9a9
    hilited_text_color: 0xffffff
    label_color: 0xa9a9a9
    name: Win10Black
    shadow_color: 0x00000000
    text_color: 0xffffff

  win10blue:
    back_color: 0xffffff
    border_color: 0xd77800
    candidate_back_color: 0x00ffffff
    candidate_shadow_color: 0x00ffffff
    candidate_text_color: 0x000000
    comment_text_color: 0x222222
    hilited_back_color: 0xd77800
    hilited_candidate_back_color: 0xd77800
    hilited_candidate_shadow_color: 0x00d77800
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_label_color: 0xffffff
    hilited_mark_color: 0xffffff
    hilited_shadow_color: 0x00d77800
    hilited_text_color: 0xffffff
    label_color: 0x838383
    name: Win10Blue
    shadow_color: 0x20000000
    text_color: 0x000000

  win10gray:
    back_color: 0xf4f4f4
    border_color: 0xcccccc
    candidate_text_color: 0x000000
    comment_text_color: 0x666666
    hilited_back_color: 0xffffff
    hilited_candidate_back_color: 0xcccccc
    hilited_candidate_text_color: 0x000000
    hilited_comment_text_color: 0x555555
    hilited_label_color: 0x444444
    hilited_mark_color: 0x444444
    hilited_text_color: 0x000000
    label_color: 0x888888
    name: Win10Gray
    shadow_color: 0x20000000
    text_color: 0x000000

  win11dark:
    name: Win11Dark
    candidate_format: " %c %@ "
    back_color: 0x303030
    border_color: 0xc202020
    candidate_back_color: 0x00303030
    candidate_text_color: 0xc9c9c9
    comment_text_color: 0xc9c9c9
    hilited_back_color: 0x202020
    hilited_candidate_back_color: 0x202020
    hilited_candidate_text_color: 0xf9f9f9
    hilited_comment_text_color: 0xf9f9f9
    hilited_label_color: 0xf9f9f9
    hilited_mark_color: 0xf9f9f9
    hilited_text_color: 0xf9f9f9
    label_color: 0xc9c9c9
    shadow_color: 0x16000000
    text_color: 0xc9c9c9
    corner_radius: 5
    hilited_corner_radius: 5
    line_spacing: 8
    hilite_spacing: 6
    spacing: 12


  win11light:
    name: Win11Light
    candidate_format: " %c %@ "
    back_color: 0xfbfbfb
    border_color: 0x10f0f0f0
    candidate_back_color: 0xfff9f9f9
    candidate_text_color: 0x000000
    comment_text_color: 0x666666
    hilited_back_color: 0xf4f4f4
    hilited_candidate_back_color: 0xf0f0f0
    hilited_candidate_text_color: 0x0A0A0A
    hilited_candidate_label_color: 0x708090
    hilited_comment_text_color: 0x1a1a1a
    hilited_mark_color: 0x303030
    hilited_text_color: 0x1a1a1a
    label_color: 0x708090
    shadow_color: 0x00000000
    text_color: 0x1a1a1a
    corner_radius: 5
    hilited_corner_radius: 5
    line_spacing: 8
    hilite_spacing: 6
    spacing: 12


  ayaya_day:
    name: "文文·晝／Ayaya Day"
    author: "Lufs X <<EMAIL>>"
    alpha: 0.95
    candidate_list_layout: linear
    inline_preedit: true
    candidate_format: "%c\u2005%@"
    corner_radius: 5
    border_height: 0
    border_width: 0
    font_face: "LXGWWenKai-Regular,PingFangSC"
    font_point: 16.5
    label_font_face: "LXGWWenKai-Regular,PingFangSC"
    label_font_point: 12
    color_space: display_p3
    back_color: 0xFFFFFF
    border_color: 0xECE4FC
    candidate_text_color: 0x121212
    comment_text_color: 0x8E8E8E
    hilited_candidate_back_color: 0xECE4FC
    hilited_candidate_label_color: 0xB18FF4
    hilited_candidate_text_color: 0x7A40EC
    label_color: 0x888785
    text_color: 0x8100EB
    hilited_corner_radius: 0

  ayaya_night:
    name: "文文·夜／Ayaya Night"
    author: "ksqsf <<EMAIL>>"
    alpha: 0.95
    candidate_list_layout: linear
    inline_preedit: true
    candidate_format: "%c\u2005%@"
    corner_radius: 5
    border_height: 0
    border_width: 0
    font_face: "LXGWWenKai-Regular,PingFangSC"
    font_point: 16.5
    label_font_face: "LXGWWenKai-Regular,PingFangSC"
    label_font_point: 12
    color_space: display_p3
    back_color: 0x1E1E1E
    border_color: 0xFF303030
    candidate_text_color: 0xAAA6F0
    comment_text_color: 0xAAA6F0
    hilited_back_color: 0xAAA6F0
    hilited_candidate_back_color: 0xA996F1
    hilited_candidate_text_color: 0x1E1E1E
    hilited_comment_text_color: 0x1E1E1E
    hilited_label_color: 0x1E1E1E
    hilited_mark_color: 0xF9F9F9
    hilited_text_color: 0xFFFFFF
    label_color: 0xAAA6F0
    text_color: 0xFFFFFF
    hilited_corner_radius: 0

  # https://gist.github.com/zsakvo/fff6e4859265d5d629439d5ccb553f8a
  wechat:
    name: '微信' # 配色取自微信键盘
    author: zsakvo
    back_color: 0xFFFFFF
    border_height: 0
    border_width: 8
    candidate_format: '%c %@ '
    comment_text_color: 0x999999
    corner_radius: 5
    hilited_corner_radius: 5
    font_face: PingFangSC
    font_point: 16
    hilited_candidate_back_color: 0x75B100
    hilited_candidate_text_color: 0xFFFFFF
    candidate_list_layout: linear
    inline_preedit: true
    label_font_point: 12
    text_color: 0x424242

  # https://gist.github.com/shlroland/78b3af88b993116c6f3e14b30cfefac0#file-yaml
  wechat_dark:
    name: '微信·暗' # 配色取自微信键盘
    author: shlroland
    back_color: 0x151515
    border_color: 0x151515
    border_height: 0
    border_width: 8
    candidate_format: '%c %@ '
    comment_text_color: 0xAEAEAE
    corner_radius: 5
    hilited_corner_radius: 5
    font_face: PingFangSC
    font_point: 16
    hilited_candidate_back_color: 0x75B100
    hilited_candidate_text_color: 0xFFFFFF
    candidate_list_layout: linear
    inline_preedit: true
    label_font_point: 12
    label_color: 0x777777
    text_color: 0xBBBBBB
  
    
  phub:
    name: 'phub' # 配色取自phub
    author: phub
    name: phub
    back_color: 0x000000
    border_color: 0x3FA0F0
    text_color: 0xFFFFFF
    hilited_text_color: 0xFFFFFF
    hilited_back_color: 0x000000
    hilited_candidate_text_color: 0x000000
    hilited_candidate_back_color: 0x3FA0F0
    candidate_text_color: 0xFFFFFF
    comment_text_color: 0xFFFFFF

  black_moqi:
    name: '黑墨奇'
    author: 墨奇
    back_color: 0xFFFFFF
    shadow_color: 0x00000000
    border_color: 0x000000
    text_color: 0x000000
    hilited_text_color: 0x000000
    hilited_back_color: 0xDDDDDD
    hilited_shadow_color: 0x00000000
    hilited_mark_color: 0xFFFFFF
    hilited_label_color: 0xFFFFFF
    hilited_candidate_text_color: 0xFFFFFF
    hilited_comment_text_color: 0xFFFFFF
    hilited_candidate_back_color: 0x000000
    hilited_candidate_border_color: 0x00000000
    hilited_candidate_shadow_color: 0x00000000
    label_color: 0x000000
    candidate_text_color: 0x000000
    comment_text_color: 0x000000
    candidate_back_color: 0x00000000
    candidate_border_color: 0x00000000
    candidate_shadow_color: 0x00000000
    prevpage_color: 0x00000000
    nextpage_color: 0x00000000




app_options:
  com.apple.Spotlight:
    ascii_mode: true
  com.alfredapp.Alfred:
    ascii_mode: true
  com.runningwithcrayons.Alfred-2:
    ascii_mode: true
  com.blacktree.Quicksilver:
    ascii_mode: true
  com.apple.Terminal:
    ascii_mode: true
    no_inline: true
  com.googlecode.iterm2:
    ascii_mode: true
    no_inline: true
  org.vim.MacVim:
    ascii_mode: true  # 初始爲西文模式
    no_inline: true   # 不使用行內編輯
    vim_mode: true    # 退出VIM插入模式自動切換輸入法狀態
  com.apple.dt.Xcode:
    ascii_mode: true
  com.barebones.textwrangler:
    ascii_mode: true
  com.macromates.TextMate.preview:
    ascii_mode: true
  com.github.atom:
    ascii_mode: true
  com.microsoft.VSCode:
    ascii_mode: true
  com.sublimetext.2:
    ascii_mode: true
  org.gnu.Aquamacs:
    ascii_mode: true
  org.gnu.Emacs:
    ascii_mode: true
  co.zeit.hyper:
    ascii_mode: true
  com.google.Chrome:
    # 規避 https://github.com/rime/squirrel/issues/435
    inline: true
  ru.keepcoder.Telegram:
    # 規避 https://github.com/rime/squirrel/issues/475
    inline: true