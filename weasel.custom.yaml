# weasel.custom.yaml
# Patch settings for weasel.yaml

patch:
  # [app_options]
  # 针对特定应用的设置
  app_options:
    Adobe Audition.exe:
        ascii_mode: true
        ascii_punct: true
    Adobe Premiere Pro.exe:
        ascii_mode: true
        ascii_punct: true
    Photoshop.exe:
        ascii_mode: true
        ascii_punct: true
    cursor.exe:
        ascii_mode: false
        ascii_punct: true
    WindowsTerminal.exe:
        ascii_mode: true
        ascii_punct: true
    cmd.exe:
        ascii_mode: true
        ascii_punct: true
    idea64.exe:
        ascii_mode: false
        ascii_punct: true
    Code.exe:
        ascii_mode: false
        ascii_punct: true
    firefox.exe:
      inline_preedit: true # 行内显示预编辑区：规避 <https://github.com/rime/weasel/issues/946>
    # cmd.exe:               # 带 .exe 的进程名：Weasel 15.0 及之前版本须小写; PR #1049 合并后释出的版本大小写不敏感
    #   ascii_mode: true     # 英文模式
    # conhost.exe:
    #   ascii_mode: true
    # windowsterminal.exe:
    #   ascii_mode: true
    # wt.exe:
    #   ascii_mode: true
    # pwsh.exe:
    #   ascii_mode: true
    # powershell.exe:
    #   ascii_mode: true
    # mintty.exe:
    #   ascii_mode: true
    # nvim-qt.exe:
    #   ascii_mode: true
    #   vim_mode: true       # vim 模式, Esc <C-c> <C-[> 切换到 ascii 状态
  # [End of <app_options>]

  # [global settings]
  show_notifications: true                   # 是否显示状态变化的通知：true；false；option_list（方案内的开头 option）
  show_notifications_time: 1200              # 通知显示的时间，单位 ms
  global_ascii: false                        # 切换为 ascii 模式时，是否影响所有窗口：true；false
  # [End of <global settings>]

  # [style]
  # 字体；候选项、候选窗口的行为、布局及样式
  style:
    color_scheme: "Customed"      # 默认配色方案 (取自重复键的后者)
    font_face: "Segoe UI Emoji, 霞鹜文楷, SF Pro, Noto Color Emoji"       # 全局字体 (取自重复键的后者)
    label_font_face: "霞鹜文楷"       # 标签字体 (取自重复键的后者)
    comment_font_face: "霞鹜文楷"     # 注释字体 (取自重复键的后者)
    font_point: 14                           # 全局字体字号 (取自重复键的后者)
    label_font_point: 13                    # 标签字体字号 (取自重复键的后者)
    comment_font_point: 13                  # 注释字体字号 (取自重复键的后者)
    # border_width: 2.1                # 早期定义, 后续 layout 中有定义

    inline_preedit: true                     # 行内显示预编辑区 (取自重复键的后者)
    preedit_type: composition                # 预编辑区内容 (取自重复键的后者)

    fullscreen: false                        # 候选窗口全屏显示 (取自重复键的后者)
    horizontal: true                        # 候选项横排 (取自重复键的后者)
    vertical_text: false                     # 竖排文本 (取自重复键的后者)
    # text_orientation: horizontal           # 文本排列方向，效果和 `vertical_text` 相同：horizontal；vertical
    vertical_text_left_to_right: true       # 竖排方向是否从左到右 (取自重复键的后者)
    vertical_text_with_wrap: false           # 文本竖排模式下，自动换行 (取自重复键的后者)
    vertical_auto_reverse: false             # 文本竖排模式下，候选窗口位于光标上方时倒序排列 (取自重复键的后者)

    label_format: "%s"                       # 标签字符 (取自重复键的后者)
    mark_text: ""                           # 标记字符 (取自重复键的后者)
    ascii_tip_follow_cursor: true           # 切换 ASCII 模式时提示跟随鼠标 (取自重复键的后者)
    enhanced_position: true                  # 无法定位候选框时，在窗口左上角显示 (取自重复键的后者)
    display_tray_icon: false                 # 托盘显示独立于语言栏的额外图标 (取自重复键的后者)
    antialias_mode: default                  # 次像素反锯齿设定 (取自重复键的后者)
    candidate_abbreviate_length: 0          # 候选项略写长度 (取自重复键的后者)
    # mouse_hover_ms: 0                      # 已弃用 (取自重复键的后者)
    hover_type: none                  # 鼠标悬停效果，可选值：none（无）、alpha（透明）、highlight（高亮）、semi_hilite（半高亮）
    
    paging_on_scroll: false                   # 滚轮行为 (取自重复键的后者)
    click_to_capture: false                  # 点击创建截图 (取自重复键的后者)

    layout:
      # baseline: 0                            # 早期定义
      # linespacing: 0                         # 早期定义
      align_type: center                    # 对齐方式 (取自重复键的后者)
      max_height: 2800                        # 最大高度 (取自重复键的后者)
      max_width: 2800                           # 最大宽度 (取自重复键的后者)
      min_height: 0                          # 最小高度 (取自重复键的后者)
      min_width: 80                          # 最小宽度 (取自重复键的后者)
      border_width: 1                       # 边框宽度 (取自重复键的后者)
      margin_x: 12                            # 左右边距 (取自重复键的后者)
      margin_y: 12                            # 上下边距 (取自重复键的后者)
      spacing: 18                            # 编码与候选间距 (取自重复键的后者)
      candidate_spacing: 15                  # 候选项间距 (取自重复键的后者)
      line_spacing: 5                        # 早期定义, 后续 layout 中未重复定义
      hilite_spacing: 4                      # 候选项与标签间距 (取自重复键的后者)
      hilite_padding: 5                      # 高亮区域与文字间距 (取自重复键的后者)
      hilite_padding_x: 5                  # 高亮区域左右间距 (取自重复键的后者)
      hilite_padding_y: 5                  # 高亮区域上下间距 (取自重复键的后者)
      shadow_radius: 2                       # 阴影半径 (取自重复键的后者)
      shadow_offset_x: "3"                     # 阴影左右偏移 (取自重复键的后者)
      shadow_offset_y: "3"                     # 阴影上下偏移 (取自重复键的后者)
      corner_radius: 8                       # 窗口圆角半径 (取自重复键的后者)
      round_corner: 8                        # 高亮背景圆角半径 (取自重复键的后者)
      # type: horizontal                       # 布局类型 (取自重复键的后者, 已注释)
      # border: 1                           # 在重复键的后者 layout 中未找到, 使用 border_width

  color_scheme_dark: "Customed" # 取自重复键的后者

  preset_color_schemes:
    jianchun:
      name: 简纯
      author: amzxyz
      back_color: '0xf2f2f2'
      border_color: '0xCE7539'
      text_color: '0x3c647e'
      hilited_text_color: '0x3c647e'
      # hilited_back_color: '0x797954'
      hilited_comment_text_color: '0xffffff'
      hilited_candidate_text_color: '0xffffff'
      hilited_candidate_back_color: '0xf0f0f0'
      hilited_label_color: '0x191919'
      candidate_text_color: '0x000000'
      comment_text_color: '0x000000'
      label_color: '0x91897e'

    win11_light:
      name: "Win11浅色 / Win11light"
      text_color: 0x191919
      label_color: 0x191919
      # border_color: 0xCE7539
      hilited_label_color: 0x191919
      back_color: 0xf9f9f9
      border_color: 0x009e5a00
      border_width: 2
      hilited_mark_color: 0xc06700
      hilited_candidate_back_color: 0xf0f0f0
      shadow_color: 0x20000000

    win11_dark:
      name: "Win11暗色 / Win11Dark"
      text_color: 0xf9f9f9
      label_color: 0xf9f9f9
      back_color: 0x2C2C2C
      hilited_label_color: 0xf9f9f9
      border_color: 0x002C2C2C
      hilited_mark_color: 0xFFC24C
      hilited_candidate_back_color: 0x383838
      shadow_color: 0x20000000

    mac_light:
      name: "Mac 白"
      text_color: 0x000000
      back_color: 0xffffff
      border_color: 0xe9e9e9
      label_color: 0x999999
      hilited_text_color: 0x000000
      hilited_back_color: 0xffffff
      candidate_text_color: 0x000000
      comment_text_color: 0x999999
      hilited_candidate_text_color: 0xffffff
      hilited_comment_text_color: 0xdddddd
      # hilited_comment_text_color: 0xdddddd # 重复键, 保留一个
      hilited_candidate_back_color: 16740656
      hilited_label_color: 0xffffff

    wechat:
      name: "微信／Wechat"
      text_color: 0x424242
      label_color: 0x999999
      back_color: 0xFFFFFF
      border_color: 0xFFFFFF
      comment_text_color: 0x999999
      candidate_text_color: 0x3c3c3c
      hilited_comment_text_color: 0xFFFFFF
      hilited_back_color: 0x79af22
      hilited_text_color: 0xFFFFFF
      hilited_label_color: 0xFFFFFF
      hilited_candidate_back_color: 0x79af22
      shadow_color: 0x20000000

    Lumk_light:
      name: "鹿鸣／Lumk light"
      author: "Lumk X <<EMAIL>>"
      back_color: 0xF9F9F9
      border_color: 0xE2E7F5
      candidate_text_color: 0x121212
      comment_text_color: 0x8E8E8E
      hilited_candidate_back_color: 0xECE4FC
      hilited_candidate_label_color: 0xB18FF4
      hilited_candidate_text_color: 0x7A40EC
      hilited_label_color: 0xA483EC
      hilited_mark_color: 0x7A40EC
      label_color: 0x888785
      text_color: 0x8100EB
      shadow_color: 0x20000000

    Customed:
      name: "Customized"
      author: 五笔小筑 <<EMAIL>>
      back_color: 0xdaf4f4f4
      shadow_color: 0xf2747474
      border_color: 0xdaf4f4f4
      text_color: 0xf2747474
      hilited_text_color: 0xff262626
      hilited_back_color: 0x00ffffff
      hilited_shadow_color: 0x00ffffff
      hilited_mark_color: 0xff218fff
      hilited_label_color: 0xffffffff
      hilited_candidate_text_color: 0xffffffff
      hilited_comment_text_color: 0xfff7f7f7
      hilited_candidate_back_color: 0xee6ca32d
      hilited_candidate_border_color: 0x00ffffff
      hilited_candidate_shadow_color: 0xff4c4c4c
      label_color: 0xff202020
      candidate_text_color: 0xff202020
      comment_text_color: 0xff494949
      candidate_back_color: 0x00494949
      candidate_border_color: 0x00494949
      candidate_shadow_color: 0x00494949
