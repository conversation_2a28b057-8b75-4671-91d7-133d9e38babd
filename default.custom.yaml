patch:
  
  # menu/page_size: 5
  
  # switcher/save_options:
  #   - ascii_punct
  #   - s2t
  #   - s2hk
  #   - s2tw
  #   - emoji
  #   - full_shape
  #   - prediction
  #   - super_tips
  #   - charset_filter
  #   - chaifen_switch
  #   - tone_display
  #   - fuzhu_switch
  #   - chinese_english
  #   - search_single_char
  
  key_binder/bindings:
    # Tab / Shift+Tab 切换光标至下/上一个拼音
    - { when: composing, accept: Shift+Tab, send: Shift+Left }

    # Option/Alt + ←/→ 切换光标至下/上一个拼音
    - { when: composing, accept: Alt+Left, send: Shift+Left }
    - { when: composing, accept: Alt+Right, send: Shift+Right }

    # 翻页 - =
    - { when: has_menu, accept: minus, send: Page_Up }
    - { when: has_menu, accept: equal, send: Page_Down }


    # 两种按键配置，鼠须管 Control+Shift+4 生效，小狼毫 Control+Shift+dollar 生效，都写上了。
    - { when: always, toggle: ascii_punct, accept: Control+。 }              # 切换中英标点
    - { when: always, toggle: ascii_punct, accept: Control+period }     # 切换中英标点
    - { when: always, toggle: s2s, s2t, s2hk, s2tw, accept: Control+Shift+F }       # 切换简繁
    - { when: always, toggle: s2s, s2t, s2hk, s2tw, accept: Control+Shift+F }  # 切换简繁
    - { when: always, toggle: full_shape, accept: Control+Shift+5 }             # 切换全半角
    - { when: always, toggle: full_shape, accept: Control+Shift+percent }       # 切换全半角
    - { when: composing, accept: Control+k, send: Shift+Delete }

    # 将小键盘 0~9 . 映射到主键盘，数字金额大写的 Lua 如 R1234.5678 可使用小键盘输入
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal,  send: period  , when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add,      send: plus,     when: composing}
    - {accept: KP_Subtract, send: minus,    when: composing}
    - {accept: KP_Divide,   send: slash,    when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
