# Rime schema
# encoding: utf-8

# 全拼和各个双拼有部分拼写规则不通用，需要修改
###############选择与之匹配的拼音方案#####################
set_shuru_schema:
  __include: 全拼    #可选的选项有（全拼, 自然码, 小鹤双拼, 微软双拼, 搜狗双拼, 智能ABC, 紫光双拼, 拼音加加）

######################################################

schema:
  schema_id: wanxiang_en
  name: 万象：英文词库
  version: "LTS"
  author:
  description: Easy English Nano，只包含少量常用词汇，方便中英文混合输入度方案调用。

switches:
  - name: ascii_mode
    reset: 0
    states: [ASCII-OFF, ASCII-ON]

engine:
  processors:
    - ascii_composer
    - key_binder
    - speller
    - recognizer
    #關閉標點符號轉換（對應symbols.yaml）    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - matcher
    - ascii_segmentor
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - table_translator
    - punct_translator
  filters:
    - uniquifier

speller:
  alphabet: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA
  delimiter: " '"
  algebra:
    __include: set_shuru_schema

translator:
  dictionary: wanxiang_en
  spelling_hints: 9

key_binder:
  import_preset: default

recognizer:
  import_preset: default


# 通用的派生规则
algebra_common:
  # 数字派生
  - derive/1([4-7|9])/$1teen/
  - derive/11/eleven/
  - derive/12/twelve/
  - derive/13/thirteen/
  - derive/15/fifteen/
  - derive/18/eighteen/
  - derive/0/o/ # 如 1000 -> oneOOO
  - derive/0/O/
  - derive/0/zero/
  - derive/1/one/
  - derive/10/ten/
  - derive/2/to/
  - derive/2/two/
  - derive/3/three/
  - derive/4/for/
  - derive/4/four/
  - derive/5/five/
  - derive/6/six/
  - derive/7/seven/
  - derive/8/eight/
  - derive/9/nine/
  # 符号派生
  - derive/\+/plus/
  - derive/\./dot/
  - derive/@/at/
  - derive/-/hyphen/
  - derive/#/hash/
  - derive/#/number/
  - derive/#/sharp/
  - derive/♯/sharp/
  - derive / slash
  - derive/&/and/
  - derive/%/percent/
  # 派生无单个特殊字符的拼写
  - derive/[.]//
  - derive/[+]//
  - derive/[@]//
  - derive/[-]//
  - derive/[_]//
  # 派生无任何非字母数字字符的拼写
  - derive/[^a-zA-Z0-9]//
  # 禁用非英文、数字开头的编码
  - erase/^[^a-zA-Z0-9].+$/
  # 全小写
  - derive/^.+$/\L$0/
  # 全大写
  - derive/^.+$/\U$0/
  # 首字母大写
  - derive/^./\U$0/
  # 前 2~10 个字母大写
  - derive/^([a-z]{2})/\U$1/
  - derive/^([a-z]{3})/\U$1/
  - derive/^([a-z]{4})/\U$1/
  - derive/^([a-z]{5})/\U$1/
  - derive/^([a-z]{6})/\U$1/
  - derive/^([a-z]{7})/\U$1/
  - derive/^([a-z]{8})/\U$1/
  - derive/^([a-z]{9})/\U$1/
  - derive/^([a-z]{10})/\U$1/

全拼:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/shi$1/
    - derive/([1-9])0000(?!0)/$1wan/
    - derive/([1-9])000(?!0)/$1qian/
    - derive/([1-9])00(?!0)/$1bai/
    - derive/([2-9])0(?!0)/$1shi/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1shi$2/
    - derive/\./dian/
    - derive/10/shi/
    - derive/0/ling/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/liang/
    - derive/3/san/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/liu/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jiu/
    - derive/\+/jia/
    - derive/#/jing/
自然码:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/ly/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/ld/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jw/
    - derive/#/jy/
小鹤双拼:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bd/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/lk/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/ll/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jx/
    - derive/#/jk/
微软双拼:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/l;/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/or/
    - derive/2/ld/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jw/
    - derive/#/j;/
搜狗双拼:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/l;/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/or/
    - derive/2/ld/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jw/
    - derive/#/jy/
智能ABC:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/vi$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qw/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1vi/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1vi$2/
    - derive/\./dw/
    - derive/10/vi/
    - derive/0/ly/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/or/
    - derive/2/lt/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lr/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jr/
    - derive/\+/jd/
    - derive/#/jy/
紫光双拼:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ii$1/
    - derive/([1-9])0000(?!0)/$1wr/
    - derive/([1-9])000(?!0)/$1qf/
    - derive/([1-9])00(?!0)/$1bp/
    - derive/([2-9])0(?!0)/$1ii/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ii$2/
    - derive/\./df/
    - derive/10/ii/
    - derive/0/l;/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/oj/
    - derive/2/lg/
    - derive/3/sr/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lj/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jj/
    - derive/\+/jx/
    - derive/#/j;/
拼音加加:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ii$1/
    - derive/([1-9])0000(?!0)/$1wf/
    - derive/([1-9])000(?!0)/$1qj/
    - derive/([1-9])00(?!0)/$1bs/
    - derive/([2-9])0(?!0)/$1ii/Add commentMore actions
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ii$2/
    - derive/\./dj/
    - derive/10/ii/
    - derive/0/lq/
    - derive/1/yi/
    - derive/2/eq/
    - derive/2/lh/
    - derive/3/sf/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/ln/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jn/
    - derive/\+/jb/
    - derive/#/jq/
国标双拼:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wf/
    - derive/([1-9])000(?!0)/$1qd/
    - derive/([1-9])00(?!0)/$1bk/
    - derive/([2-9])0(?!0)/$1ui/Add commentMore actions
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dd/
    - derive/10/ui/
    - derive/0/lj/
    - derive/1/yi/
    - derive/2/er/
    - derive/3/sf/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/ly/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jy/
    - derive/\+/jq/
    - derive/#/jj/